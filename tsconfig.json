{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env", "webpack", "jest"], "paths": {}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["packages/**/*.ts", "packages/**/*.tsx", "packages/**/*.vue"], "exclude": ["node_modules"]}