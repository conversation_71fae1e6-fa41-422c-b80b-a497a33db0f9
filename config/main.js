const { GLOBAL_DEPENDENCY_MAP } = require('@zeta/external-dependencies');

module.exports = {
    stories: ['../packages/**/*.stories.(js|jsx|ts|tsx|mdx)'],
    addons: [
        '@storybook/addon-actions',
        {
            name: '@storybook/addon-docs',
            options: {
                babelOptions: {
                    presets: [
                        [
                            '@vue/cli-plugin-babel/preset',
                            {
                                jsx: false,
                            },
                        ],
                    ],
                },
            },
        },
        '@storybook/addon-knobs',
        '@storybook/addon-links',
        '@storybook/addon-notes',
        '@storybook/addon-viewport',
    ],
    webpack: async (config) => {
        config.externals = GLOBAL_DEPENDENCY_MAP;
        return config;
    },
};
