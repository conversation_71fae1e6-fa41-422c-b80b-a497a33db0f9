{"name": "real-time-workers", "description": "> TODO: description", "private": true, "scripts": {"clean": "lerna clean --yes", "build": "turbo run build", "build:no-cache": "turbo run build --concurrency=1 --no-cache --force", "test": "lerna run test", "prerelease": "npm run test && npm run build", "release": "hercules-component release", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix"}, "workspaces": ["packages/*"], "prettier": "@zeta/prettier-config", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "~5.0.8", "@vue/devtools-api": "^6.5.1", "@vue/eslint-config-typescript": "^9.1.0", "@zeta/prettier-config": "^1.0.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.4.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.5.0", "husky": "^4.3.8", "lerna": "^8.0.2", "lint-staged": "^11.2.6", "prettier": "^3.3.2", "turbo": "^1.11.3", "webpack": "^5.89.0"}, "overrides": {}, "gitHooks": {"pre-commit": "lint-staged"}, "version": "2.0.0", "dependencies": {"dayjs": "1.11.13"}}