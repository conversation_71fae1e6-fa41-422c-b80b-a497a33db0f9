{"name": "@zeta-business/worker-list", "version": "0.2.0", "description": "> TODO: description", "author": {"name": "WebInfraCiCd(create-hercules-business-component)", "email": "<EMAIL>"}, "homepage": "https://https///****************************************", "main": "lib/index.js", "modules": "lib/index.js", "files": ["lib", "src"], "scripts": {"build": "hercules-component build", "serve": "hercules-component serve", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@hercules/component-core": "~3.1.0", "@vicons/ionicons5": "0.13.0", "@vueuse/core": "^10.7.1", "@zeta-gds/components": "2.2.0", "@zeta-gds/themes.aphrodite": "2.2.0", "@zeta/service-clients": "^1.77.13", "vue": "~3.3"}, "devDependencies": {"@hercules/component-cli": "~3.1.0", "@types/jest": "^27.4.1", "@types/webpack": "^5.28.5", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-typescript": "~5.0.8", "@vue/cli-plugin-unit-jest": "^5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/test-utils": "^2.4.1", "babel-jest": "^27.5.1", "babel-plugin-require-context-hook": "^1.0.0", "jest": "^27.1.0", "jest-mock-axios": "^4.0.0", "sass": "^1.58.3", "sass-loader": "^13.2.0", "ts-jest": "^27.0.4", "typescript": "~4.9.5"}}