import { shallowMount } from '@vue/test-utils';
import WorkerList from '@/worker-list/WorkerList.vue';

describe('WorkerList.vue', () => {
    it('renders props.msg when passed', () => {
        const message = 'new message';
        const wrapper = shallowMount(WorkerList, {
            propsData: { message },
        });

        expect(wrapper.text()).toMatch(message);
    });

    it('renders i18n locale', () => {
        const message = 'message';
        const wrapper = shallowMount(WorkerList, {
            propsData: { message, locale: 'pt-BR' },
        });

        expect(wrapper.text()).toMatch('olá i18n !!');
    });
});
