import { getAuthToken } from '@hercules/context';
import {
    RequestExecutorService,
} from '@zeta/service-clients';



export const auraAppServiceManager = () => {
    let instance: any;
    const resolveAuthToken = () => {
        return { Authorization: 'Bearer ' + getAuthToken() };
    };
    return function (baseURL: string) {
        instance =
            instance ||
            new RequestExecutorService({
                baseURL: baseURL,
                resolveAuthToken,
            });
        return instance;
    };
};