import { context, getAuthToken } from '@hercules/context';
import { AccessManagementService } from '@zeta/service-clients';

const resolveAuthToken = () => {
    return {
        Authorization: `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json',
    };
};

export const accessManagementServiceInstance = new AccessManagementService({
    baseURL: context.getAttribute('accessManagement') as any,
    resolveAuthToken,
});
