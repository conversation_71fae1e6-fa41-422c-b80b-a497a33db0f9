import {auraAppServiceManager} from './workerListManager.service';
import dayjs from 'dayjs';
import utc   from 'dayjs/plugin/utc';

dayjs.extend(utc);
const auraAppService = auraAppServiceManager();


export const getRealTimeWorkerData = async (
    payload: any,
) => {

    const {tenantId, coaid, cbuid, atlantaUrl} = payload;
    
    // Format cbuid to YYYY-MM-DD format
    const formattedCbuid = dayjs(Number(cbuid)).format("YYYY-MM-DD");
   
    const phase = 'ACTIVE'; // phase will be always ACTIVE for real time workers
    const requestBody = {
        serviceName: 'AURA_APP_SERVICE',
        path: `/pda-manager/tenants/${tenantId}/coas/${coaid}/cbus/${formattedCbuid}/phases/${phase}/realTimeWorkers`,
        method: 'GET',
        tenantId: tenantId,
    };
    const response = await auraAppService(atlantaUrl).executeRequest(requestBody);

    return response;
};