<template>
    <z-card :class="{ 'highlighted-card': highlighted }" class="worker-card" role="region" aria-label="Worker card">
        <div class="card-header">
            <div class="name-section">
                <div class="worker-name-container">
                    <z-popover trigger="hover">
                        <template #trigger>
                            <z-text strong tag="h3" class="worker-name">
                                {{ name }}
                            </z-text>
                        </template>
                        <!-- <span>Any content can be placement in the popover</span> -->
                        <div >
                            <div>
                                <div>
                                    <span class="tooltip-label">Code</span>
                                    <span class="tooltip-value">{{ code }}</span>
                                </div>
                                <div class="tooltip-row">
                                    <span class="tooltip-label">Description</span>
                                    <span class="tooltip-value">{{ description }}</span>
                                </div>
                                <div class="tooltip-row">
                                    <span class="tooltip-label">Cluster</span>
                                    <span class="tooltip-value">{{ clusterName }}</span>
                                </div>
                            </div>
                        </div>
                    </z-popover>
                    <!-- <div class="tooltip">
            <div class="tooltip-content">
              <div class="tooltip-row">
                <span class="tooltip-label">Code</span>
                <span class="tooltip-value">{{ code }}</span>
              </div>
              <div class="tooltip-row">
                <span class="tooltip-label">Description</span>
                <span class="tooltip-value">{{ description }}</span>
              </div>
              <div class="tooltip-row">
                <span class="tooltip-label">Cluster</span>
                <span class="tooltip-value">{{ clusterName }}</span>
              </div>
            </div>
          </div> -->
                </div>
                <z-icon v-if="status === 'UNHEALTHY'" color="#F04F6D" :size="20">
                    <AlertCircle />
                </z-icon>
            </div>
            <z-tag :bordered="false" v-if="status === 'SLOW'" class="status-tag">
                <template #icon>
                    <z-icon color="#F04F6D" :size="12">
                        <Ellipse />
                    </z-icon>
                </template>
                Slow
            </z-tag>
        </div>
        <div class="card-content">
            <div class="stats-left">
                <div class="posting-stats-line">
                    <z-text class="success-count" strong>{{ successCount }}</z-text>
                    <z-text class="posting-total" strong>/ {{ formatCount(totalPostings) }} postings</z-text>
                </div>
                <z-text class="success-label">Successfully processed</z-text>
            </div>
            <z-divider vertical class="divider" />
            <div class="stats-right">
                <div class="failed-line">
                    <z-text class="failed-count" strong>{{ formatCount(failedCount) }}</z-text>
                    <z-text class="failed-label">Failed</z-text>
                </div>
                <div class="queue-line">
                    <z-text class="queue-count" strong>{{ formatCount(queueCount) }} </z-text>
                    <z-text class="queue-label">In queue</z-text>
                </div>
            </div>
        </div>
    </z-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { ZCard, ZText, ZDivider, ZTag, ZIcon, ZPopover } from '@zeta-gds/components';
import { Ellipse, AlertCircle } from '@vicons/ionicons5';

export default defineComponent({
    name: 'WorkerCard',
    components: {
        ZCard,
        ZText,
        ZDivider,
        ZTag,
        ZIcon,
        Ellipse,
        AlertCircle,
        ZPopover,
    },
    props: {
        name: {
            type: String,
            required: true,
        },
        code: {
            type: String,
            required: true,
        },
        description: {
            type: String,
            required: true,
        },
        clusterName: {
            type: String,
            required: true,
        },
        successCount: {
            type: Number,
            required: true,
        },
        totalPostings: {
            type: Number,
            required: true,
        },
        failedCount: {
            type: Number,
            required: true,
        },
        queueCount: {
            type: Number,
            required: true,
        },
        highlighted: {
            type: Boolean,
            default: false,
        },
        status: {
            type: String,
            required: true,
        },
    },
    methods: {
        formatCount(value: number): string {
            if (value >= 1000) {
                // divide by 1,000 and keep one decimal place
                const formattedValue = (value / 1000).toFixed(1);
                // if it’s something like “1.0”, drop the “.0”
                const compact = formattedValue.replace(/\.0$/, '');
                return `${compact}k`;
            }
            return value.toString();
        },
    },
});
</script>

<style scoped lang="scss">
.worker-card {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    background-color: white;
    border-radius: 4px;
    box-shadow: none;
    border: 1px solid #e0e0e0;
}

.highlighted-card {
    background-color: #f8d7da; /* light pink */
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
}

.name-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-icon {
    color: #d32f2f;
    width: 1.25rem;
    height: 1.25rem;
}

// .status-tag {
//   font-size: 0.75rem;
//   padding: 0.25rem 0.5rem;
// }

.worker-name-container {
    position: relative;
    display: inline-block;
}

.worker-name {
    color: #0a1f44; /* dark blue */
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
    border-bottom: 1px dashed rgb(224, 224, 224); /* dashed underline */
    cursor: pointer;
    // padding-bottom: 0.25rem;
}

.tooltip {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition:
        opacity 0.2s,
        visibility 0.2s;
    margin-top: 0.5rem;
}

.worker-name-container:hover .tooltip {
    opacity: 1;
    visibility: visible;
}

.tooltip-content {
    background: #333;
    color: white;
    padding: 0.75rem;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    white-space: nowrap;
    min-width: 200px;
}

.tooltip-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.tooltip-row:last-child {
    margin-bottom: 0;
}

.tooltip-label {
    font-weight: 600;
    margin-right: 1rem;
}

.tooltip-value {
    color: #e0e0e0;
}

.card-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stats-left,
.stats-right {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.posting-stats-line {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
}

.failed-line,
.queue-line {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
}

.success-count {
    color: #2e7d32; /* green */
    font-weight: 700;
    font-size: 1.25rem;
    // white-space: nowrap;
}

.posting-total {
    color: #434b79;
    font-weight: 600;
    font-size: 0.875rem;
}

.success-label {
    color: #4a4a4a;
    font-size: 0.75rem;
}

.failed-count {
    color: #b71c1c; /* red */
    font-weight: 700;
    font-size: 1.25rem;
    // white-space: nowrap;
}

.failed-label {
    color: #a0a0a0;
    font-size: 0.75rem;
}

.queue-count {
    color: #434b79; /* dark blue */
    font-weight: 700;
    font-size: 1.25rem;
    // white-space: nowrap;
}

.queue-label {
    color: #a0a0a0; /* same as failed text color */
    font-size: 0.75rem;
}

.divider {
    height: 3rem;
    border-left: 1px solid #d0d0d0;
}
</style>
