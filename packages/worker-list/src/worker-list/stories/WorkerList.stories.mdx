import { Meta, <PERSON>ps, Story, Preview } from '@storybook/addon-docs/blocks';
import { action } from '@storybook/addon-actions';
import { linkTo } from '@storybook/addon-links';
import { withKnobs, select, text } from '@storybook/addon-knobs';
import WorkerList from '../WorkerList.vue';

<Meta title="Components|WorkerList" component={ WorkerList } decorator={[withKnobs]} />

# WorkerList

<Props of={ WorkerList } />

This is a simple component with some text in it.

<Preview>
    <Story name="Story Label">
        {{
            components: { WorkerList },
            template: '<worker-list :message="message" :locale="locale" @click="action"></worker-list>',
            props: {
                message: {
                    type: String,
                    default: text('Message', 'Hello World'),
                },
                locale: {
                    type: String,
                    default: select(
                        'Select locale',
                        { 'Indian English': 'en-IN', 'Brazilian Portuguese': 'pt-BR' },
                        'en-IN',
                    ),
                },
            },
            methods: { action: action('clicked') },
        }}
    </Story>
</Preview>
